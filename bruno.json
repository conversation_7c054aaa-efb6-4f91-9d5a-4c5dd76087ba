{"info": {"name": "PhonePe API Test Collection", "description": "Bruno collection for testing PhonePe Static QR, Dynamic QR, and EDC APIs"}, "envs": [{"name": "local", "vars": {"baseUrl": "http://localhost:3000"}}], "requests": [{"name": "Static QR Init", "method": "POST", "url": "{{baseUrl}}/api/phonepe/static-qr/init", "body": {"restaurantId": "store1", "tableNumber": "table1"}}, {"name": "Static QR Enable", "method": "POST", "url": "{{baseUrl}}/api/phonepe/static-qr/enable", "body": {"qrId": "<qrId>"}}, {"name": "Static QR Disable", "method": "POST", "url": "{{baseUrl}}/api/phonepe/static-qr/disable", "body": {"qrId": "<qrId>"}}, {"name": "Dynamic QR Create", "method": "POST", "url": "{{baseUrl}}/api/phonepe/dynamic-qr/create", "body": {"amount": 100, "storeId": "store1", "terminalId": "terminal1", "transactionId": "txn123456"}}, {"name": "Dynamic QR Cancel", "method": "POST", "url": "{{baseUrl}}/api/phonepe/dynamic-qr/cancel", "body": {"transactionId": "txn123456"}}, {"name": "Dynamic QR Status", "method": "POST", "url": "{{baseUrl}}/api/phonepe/dynamic-qr/status", "body": {"transactionId": "txn123456"}}, {"name": "Dynamic QR Refund", "method": "POST", "url": "{{baseUrl}}/api/phonepe/dynamic-qr/refund", "body": {"transactionId": "txn123456", "amount": 100, "providerReferenceId": "ref123456"}}, {"name": "Dynamic QR Refund Status", "method": "POST", "url": "{{baseUrl}}/api/phonepe/dynamic-qr/refund-status", "body": {"transactionId": "txn123456", "providerReferenceId": "ref123456"}}, {"name": "EDC Sale Init", "method": "POST", "url": "{{baseUrl}}/api/phonepe/edc/sale", "body": {"amount": 100, "storeId": "store1", "terminalId": "terminal1", "transactionId": "edcTxn123456"}}, {"name": "EDC StatusCheck", "method": "POST", "url": "{{baseUrl}}/api/phonepe/edc/status", "body": {"transactionId": "edcTxn123456"}}, {"name": "Transaction List", "method": "POST", "url": "{{baseUrl}}/api/phonepe/transaction-list", "body": {"storeId": "store1", "amount": 100, "last4TxnId": "1234"}}, {"name": "Metadata API", "method": "POST", "url": "{{baseUrl}}/api/phonepe/metadata", "body": {"txnId": "txn123456"}}]}
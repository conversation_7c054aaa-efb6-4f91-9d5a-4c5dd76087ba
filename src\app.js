require('dotenv').config();
const express = require('express');
const bodyParser = require('body-parser');
const axios = require('axios');
const phonepeConfig = require('./phonepeConfig');
const { generateSignature, decodeCallback } = require('./phonepeUtils');
const app = express();
const PORT = process.env.PORT || 3000;

app.use(bodyParser.json());

function debugRequest(apiName, payload, apiPath, xVerify) {
  console.log(`\n[DEBUG] ${apiName}`);
  console.log('API Path:', apiPath);
  console.log('Payload:', JSON.stringify(payload, null, 2));
  console.log('X-VERIFY:', xVerify);
}

// Static QR APIs
app.post('/api/phonepe/static-qr/init', async (req, res) => {
  // Required: storeId, orderId (QR identifier), message, amountRestrictionType, activeTill (validity)
  // orderId = Unique identifier for this Static QR code (e.g., "table_5_qr", "counter_1_qr")
  // activeTill = Unix timestamp when QR expires (validity period)
  const { storeId, orderId, message, amountRestrictionType = "UNKNOWN", activeTill, amount, minAmount, terminalId } = req.body;
  if (!storeId) {
    return res.status(400).json({ error: 'storeId and orderId are required' });
  }

  const payload = {
    merchantId: phonepeConfig.merchantId,
    storeId: storeId,
    orderId: orderId, // This is the QR code identifier, not a payment order
    message: message || `Payment for ${orderId}`,
    amountRestrictionType,
    terminalId,
    activeTill: activeTill || Math.floor(Date.now() / 1000) + (3000 * 24 * 60 * 60), // Default: 30 days validity
    ...(amountRestrictionType === "FIXED_AMOUNT" && amount && { amount: amount.toString() }),
    ...(amountRestrictionType === "MIN_BOUNDED" && minAmount && { minAmount: minAmount.toString() }),
  };
  const apiPath = '/v1/sqr';
  const xVerify = generateSignature(payload, apiPath);
  debugRequest('Static QR Init', payload, apiPath, xVerify);
  try {
    const response = await axios.post(
      phonepeConfig.baseUrl + apiPath,
      { request: Buffer.from(JSON.stringify(payload)).toString('base64') },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-VERIFY': xVerify,
          'X-MERCHANT-ID': phonepeConfig.merchantId,
          'X-PROVIDER-ID': phonepeConfig.providerId,
        },
      }
    );
    console.log('Response:', response.data);
    res.json(response.data);
  } catch (err) {
    console.error('Error:', err.response?.data || err.message);
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

app.post('/api/phonepe/static-qr/enable', async (req, res) => {
  // Required: qrId
  const { qrId } = req.body;
  if (!qrId) return res.status(400).json({ error: 'qrId is required' });
  const payload = { qrId };
  const apiPath = '/v3/staticQr/enable';
  const xVerify = generateSignature(payload, apiPath);
  debugRequest('Static QR Enable', payload, apiPath, xVerify);
  try {
    const response = await axios.post(
      phonepeConfig.baseUrl + apiPath,
      { request: Buffer.from(JSON.stringify(payload)).toString('base64') },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-VERIFY': xVerify,
          'X-MERCHANT-ID': phonepeConfig.merchantId,
          'X-PROVIDER-ID': phonepeConfig.providerId,
        },
      }
    );
    console.log('Response:', response.data);
    res.json(response.data);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

app.post('/api/phonepe/static-qr/disable', async (req, res) => {
  // Required: qrId
  const { qrId } = req.body;
  if (!qrId) return res.status(400).json({ error: 'qrId is required' });
  const payload = { qrId };
  const apiPath = '/v3/staticQr/disable';
  const xVerify = generateSignature(payload, apiPath);
  debugRequest('Static QR Disable', payload, apiPath, xVerify);
  try {
    const response = await axios.post(
      phonepeConfig.baseUrl + apiPath,
      { request: Buffer.from(JSON.stringify(payload)).toString('base64') },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-VERIFY': xVerify,
          'X-MERCHANT-ID': phonepeConfig.merchantId,
          'X-PROVIDER-ID': phonepeConfig.providerId,
        },
      }
    );
    res.json(response.data);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

app.post('/api/phonepe/static-qr/callback', (req, res) => {
  const encoded = req.body.response;
  console.log('Encoded Callback Data:', encoded);
  const data = decodeCallback(encoded);
  console.log('Decoded Callback Data:', data);
  res.json({ status: 'received', data: data });
});

// Dynamic QR API
app.post('/api/phonepe/dynamic-qr/init', async (req, res) => {
  // Required: restaurantId, tableNumber, amount
  const { restaurantId, tableNumber, amount } = req.body;
  if (!restaurantId || !tableNumber || !amount) {
    return res.status(400).json({ error: 'restaurantId, tableNumber, and amount are required' });
  }
  const payload = {
    merchantId: phonepeConfig.merchantId,
    storeId: restaurantId,
    terminalId: `${restaurantId}_${tableNumber}`,
    amount: amount * 100, // in paise
    callbackUrl: phonepeConfig.callbackUrl,
  };
  const apiPath = '/v3/dynamicQr/init';
  const xVerify = generateSignature(payload, apiPath);
  debugRequest('Dynamic QR Init', payload, apiPath, xVerify);
  try {
    const response = await axios.post(
      phonepeConfig.baseUrl + apiPath,
      Buffer.from(JSON.stringify(payload)).toString('base64'),
      {
        headers: {
          'Content-Type': 'application/json',
          'X-VERIFY': xVerify,
          'X-MERCHANT-ID': phonepeConfig.merchantId,
        },
      }
    );
    res.json(response.data);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

// Dynamic QR Create API
app.post('/api/phonepe/dynamic-qr/create', async (req, res) => {
  // Required: amount, storeId, terminalId, transactionId
  const { amount, storeId, terminalId, transactionId, merchantOrderId, expiresIn = 1800 } = req.body;
  if (!amount || !storeId || !terminalId || !transactionId) {
    return res.status(400).json({ error: 'amount, storeId, terminalId, transactionId are required' });
  }
  const payload = {
    merchantId: phonepeConfig.merchantId,
    transactionId,
    merchantOrderId: merchantOrderId || transactionId,
    amount: amount * 100,
    expiresIn,
    storeId,
    terminalId,
  };
  const apiPath = '/v3/qr/init';
  const xVerify = generateSignature(payload, apiPath);
  debugRequest('Dynamic QR Create', payload, apiPath, xVerify);
  try {
    const response = await axios.post(
      phonepeConfig.baseUrl + apiPath,
      { request: Buffer.from(JSON.stringify(payload)).toString('base64') },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-VERIFY': xVerify,
          'X-MERCHANT-ID': phonepeConfig.merchantId,
          'X-PROVIDER-ID': phonepeConfig.providerId,
        },
      }
    );
    res.json(response.data);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

// Dynamic QR Cancel API
app.post('/api/phonepe/dynamic-qr/cancel', async (req, res) => {
  // Required: transactionId
  const { transactionId } = req.body;
  if (!transactionId) return res.status(400).json({ error: 'transactionId is required' });
  const payload = {
    merchantId: phonepeConfig.merchantId,
    transactionId,
  };
  const apiPath = `/v3/transaction/${phonepeConfig.merchantId}/${transactionId}/cancel`;
  // For cancel API, signature is generated with empty payload as it's a POST with no body
  const xVerify = generateSignature(null, apiPath);
  debugRequest('Dynamic QR Cancel', payload, apiPath, xVerify);
  try {
    const response = await axios.post(
      phonepeConfig.baseUrl + apiPath,
      {},
      {
        headers: {
          'Content-Type': 'application/json',
          'X-VERIFY': xVerify,
          'X-MERCHANT-ID': phonepeConfig.merchantId,
          'X-PROVIDER-ID': phonepeConfig.providerId,
        },
      }
    );
    res.json(response.data);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

// Dynamic QR Payment Status API
app.post('/api/phonepe/dynamic-qr/status', async (req, res) => {
  // Required: transactionId
  const { transactionId } = req.body;
  if (!transactionId) return res.status(400).json({ error: 'transactionId is required' });
  const apiPath = `/v3/transaction/${phonepeConfig.merchantId}/${transactionId}/status`;
  // For GET requests, signature is generated with empty payload
  const xVerify = generateSignature(null, apiPath);
  debugRequest('Dynamic QR Status', { merchantId: phonepeConfig.merchantId, transactionId }, apiPath, xVerify);
  try {
    const response = await axios.get(
      phonepeConfig.baseUrl + apiPath,
      {
        headers: {
          'Content-Type': 'application/json',
          'X-VERIFY': xVerify,
          'X-MERCHANT-ID': phonepeConfig.merchantId,
          'X-PROVIDER-ID': phonepeConfig.providerId,
        },
      }
    );
    res.json(response.data);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

// Dynamic QR Callback API
app.post('/api/phonepe/dynamic-qr/callback', (req, res) => {
  console.log('Dynamic QR Callback:', req.body);
  res.status(200).json({ status: 'received' });
});

// Dynamic QR Refund API
app.post('/api/phonepe/dynamic-qr/refund', async (req, res) => {
  // Required: transactionId, amount, providerReferenceId
  const { transactionId, amount, providerReferenceId } = req.body;
  if (!transactionId || !amount || !providerReferenceId) {
    return res.status(400).json({ error: 'transactionId, amount, providerReferenceId are required' });
  }
  const payload = {
    merchantId: phonepeConfig.merchantId,
    providerId: phonepeConfig.providerId,
    transactionId,
    amount: amount * 100,
    providerReferenceId,
  };
  const apiPath = '/v3/qr/refund';
  const xVerify = generateSignature(payload, apiPath);
  debugRequest('Dynamic QR Refund', payload, apiPath, xVerify);
  try {
    const response = await axios.post(
      phonepeConfig.baseUrl + apiPath,
      Buffer.from(JSON.stringify(payload)).toString('base64'),
      {
        headers: {
          'Content-Type': 'application/json',
          'X-VERIFY': xVerify,
          'X-MERCHANT-ID': phonepeConfig.merchantId,
        },
      }
    );
    res.json(response.data);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

// Dynamic QR Refund Status API
app.post('/api/phonepe/dynamic-qr/refund-status', async (req, res) => {
  // Required: transactionId, providerReferenceId
  const { transactionId, providerReferenceId } = req.body;
  if (!transactionId || !providerReferenceId) {
    return res.status(400).json({ error: 'transactionId, providerReferenceId are required' });
  }
  const payload = {
    merchantId: phonepeConfig.merchantId,
    providerId: phonepeConfig.providerId,
    transactionId,
    providerReferenceId,
  };
  const apiPath = '/v3/qr/refund/status';
  const xVerify = generateSignature(payload, apiPath);
  debugRequest('Dynamic QR Refund Status', payload, apiPath, xVerify);
  try {
    const response = await axios.post(
      phonepeConfig.baseUrl + apiPath,
      Buffer.from(JSON.stringify(payload)).toString('base64'),
      {
        headers: {
          'Content-Type': 'application/json',
          'X-VERIFY': xVerify,
          'X-MERCHANT-ID': phonepeConfig.merchantId,
        },
      }
    );
    res.json(response.data);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

// EDC Machine API (mock, as PhonePe EDC is hardware-based)
app.post('/api/phonepe/edc/init', (req, res) => {
  res.json({ message: 'EDC Machine integration is hardware-based. Please contact PhonePe for SDK/API.' });
});

// EDC Sale Init API
app.post('/api/phonepe/edc/sale', async (req, res) => {
  // Required: amount, storeId, terminalId, transactionId, orderId, paymentModes
  const { amount, storeId, terminalId, transactionId, orderId, paymentModes = ["CARD", "DQR"], integrationMappingType = "ONE_TO_ONE", timeAllowedForHandoverToTerminalSeconds = 60 } = req.body;
  if (!amount || !storeId || !terminalId || !transactionId || !orderId) {
    return res.status(400).json({ error: 'amount, storeId, terminalId, transactionId, orderId are required' });
  }
  const payload = {
    merchantId: phonepeConfig.merchantId,
    storeId,
    terminalId,
    orderId,
    transactionId,
    amount: amount * 100,
    paymentModes,
    integrationMappingType,
    timeAllowedForHandoverToTerminalSeconds,
  };
  const apiPath = '/v1/edc/transaction/init';
  const xVerify = generateSignature(payload, apiPath);
  debugRequest('EDC Sale Init', payload, apiPath, xVerify);
  try {
    const response = await axios.post(
      phonepeConfig.baseUrl + apiPath,
      { request: Buffer.from(JSON.stringify(payload)).toString('base64') },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-VERIFY': xVerify,
          'X-MERCHANT-ID': phonepeConfig.merchantId,
          'X-PROVIDER-ID': phonepeConfig.providerId,
          'X-CALLBACK-URL': phonepeConfig.callbackUrl,
        },
      }
    );
    res.json(response.data);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

// EDC StatusCheck API
app.post('/api/phonepe/edc/status', async (req, res) => {
  // Required: transactionId
  const { transactionId } = req.body;
  if (!transactionId) return res.status(400).json({ error: 'transactionId is required' });
  const payload = {
    merchantId: phonepeConfig.merchantId,
    providerId: phonepeConfig.providerId,
    transactionId,
  };
  const apiPath = '/v3/edc/status';
  const xVerify = generateSignature(payload, apiPath);
  debugRequest('EDC StatusCheck', payload, apiPath, xVerify);
  try {
    const response = await axios.post(
      phonepeConfig.baseUrl + apiPath,
      Buffer.from(JSON.stringify(payload)).toString('base64'),
      {
        headers: {
          'Content-Type': 'application/json',
          'X-VERIFY': xVerify,
          'X-MERCHANT-ID': phonepeConfig.merchantId,
        },
      }
    );
    res.json(response.data);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

// Transaction List API
app.post('/api/phonepe/transaction-list', async (req, res) => {
  // Required: startDate, endDate
  const { startDate, endDate } = req.body;
  if (!startDate || !endDate) {
    return res.status(400).json({ error: 'startDate and endDate are required' });
  }
  const payload = {
    merchantId: phonepeConfig.merchantId,
    providerId: phonepeConfig.providerId,
    startDate,
    endDate,
  };
  const apiPath = '/v3/transaction/list';
  const xVerify = generateSignature(payload, apiPath);
  debugRequest('Transaction List', payload, apiPath, xVerify);
  try {
    const response = await axios.post(
      phonepeConfig.baseUrl + apiPath,
      Buffer.from(JSON.stringify(payload)).toString('base64'),
      {
        headers: {
          'Content-Type': 'application/json',
          'X-VERIFY': xVerify,
          'X-MERCHANT-ID': phonepeConfig.merchantId,
        },
      }
    );
    res.json(response.data);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

// Metadata API
app.post('/api/phonepe/metadata', async (req, res) => {
  // Required: None
  const payload = {
    merchantId: phonepeConfig.merchantId,
    providerId: phonepeConfig.providerId,
  };
  const apiPath = '/v3/metadata';
  const xVerify = generateSignature(payload, apiPath);
  debugRequest('Metadata API', payload, apiPath, xVerify);
  try {
    const response = await axios.post(
      phonepeConfig.baseUrl + apiPath,
      Buffer.from(JSON.stringify(payload)).toString('base64'),
      {
        headers: {
          'Content-Type': 'application/json',
          'X-VERIFY': xVerify,
          'X-MERCHANT-ID': phonepeConfig.merchantId,
        },
      }
    );
    res.json(response.data);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

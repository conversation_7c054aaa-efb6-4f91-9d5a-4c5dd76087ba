{"info": {"_postman_id": "b1b2b3b4-b5b6-b7b8-b9ba-bbcbdbebfbfc", "name": "PhonePe API Test Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "Test all PhonePe API endpoints (Static QR, Dynamic QR, EDC, Transaction List, Metadata)"}, "item": [{"name": "Static QR Init", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"restaurantId\": \"store1\",\n  \"tableNumber\": \"table1\",\n  \"amountRestrictionType\": \"UNKNOWN\",\n  \"orderId\": \"order_123456\",\n  \"message\": \"Payment for table 1\"\n}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/static-qr/init", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "static-qr", "init"]}}}, {"name": "Static QR Enable", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"qrId\": \"QR_123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/static-qr/enable", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "static-qr", "enable"]}}}, {"name": "Static QR Disable", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"qrId\": \"QR_123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/static-qr/disable", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "static-qr", "disable"]}}}, {"name": "Dynamic QR Init", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"restaurantId\": \"store1\",\n  \"tableNumber\": \"table1\",\n  \"amount\": 100\n}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/dynamic-qr/init", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "dynamic-qr", "init"]}}}, {"name": "Dynamic QR Create", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 100,\n  \"storeId\": \"store1\",\n  \"terminalId\": \"terminal1\",\n  \"transactionId\": \"txn123456\",\n  \"merchantOrderId\": \"order123456\",\n  \"expiresIn\": 1800\n}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/dynamic-qr/create", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "dynamic-qr", "create"]}}}, {"name": "Dynamic QR Cancel", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"transactionId\": \"txn123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/dynamic-qr/cancel", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "dynamic-qr", "cancel"]}}}, {"name": "Dynamic QR Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"transactionId\": \"txn123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/dynamic-qr/status", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "dynamic-qr", "status"]}}}, {"name": "Dynamic QR Refund", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"transactionId\": \"txn123456\",\n  \"amount\": 100,\n  \"providerReferenceId\": \"ref123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/dynamic-qr/refund", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "dynamic-qr", "refund"]}}}, {"name": "Dynamic QR Refund Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"transactionId\": \"txn123456\",\n  \"providerReferenceId\": \"ref123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/dynamic-qr/refund-status", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "dynamic-qr", "refund-status"]}}}, {"name": "EDC Sale Init", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 100,\n  \"storeId\": \"store1\",\n  \"terminalId\": \"terminal1\",\n  \"transactionId\": \"edcTxn123456\",\n  \"orderId\": \"order123456\",\n  \"paymentModes\": [\"CARD\", \"DQR\"],\n  \"integrationMappingType\": \"ONE_TO_ONE\",\n  \"timeAllowedForHandoverToTerminalSeconds\": 60\n}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/edc/sale", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "edc", "sale"]}}}, {"name": "EDC StatusCheck", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"transactionId\": \"edcTxn123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/edc/status", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "edc", "status"]}}}, {"name": "Transaction List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"startDate\": \"2024-01-01\",\n  \"endDate\": \"2024-01-31\"\n}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/transaction-list", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "transaction-list"]}}}, {"name": "Metadata API", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/metadata", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "metadata"]}}}, {"name": "Static QR Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"response\": \"callback data\"\n}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/static-qr/callback", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "static-qr", "callback"]}}}, {"name": "Dynamic QR Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"response\": \"callback data\"\n}"}, "url": {"raw": "{{baseUrl}}/api/phonepe/dynamic-qr/callback", "host": ["{{baseUrl}}"], "path": ["api", "phonepe", "dynamic-qr", "callback"]}}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000"}]}
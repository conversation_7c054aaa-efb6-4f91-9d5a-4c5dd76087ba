const crypto = require('crypto');
const { saltKey, saltIndex } = require('./phonepeConfig');

// Generates X-VERIFY signature for PhonePe API
function generateSignature(payload, apiPath) {
  // For GET requests (like status check), payload is null/empty
  // For POST requests, payload contains the request data
  let base64Payload = '';
  if (payload && Object.keys(payload).length > 0) {
    base64Payload = Buffer.from(JSON.stringify(payload)).toString('base64');
  }

  // Concatenate: base64(payload) + apiPath + saltKey
  const stringToSign = base64Payload + apiPath + saltKey;
  const sha256 = crypto.createHash('sha256').update(stringToSign).digest('hex');
  return sha256 + '###' + saltIndex;
}

// Decode base64-encoded callback payload
function decodeCallback(encoded) {
  const jsonString = Buffer.from(encoded, 'base64').toString('utf8');
  return JSON.parse(jsonString);
}

module.exports = { generateSignature, decodeCallback };
